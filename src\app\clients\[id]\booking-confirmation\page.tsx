"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Roboto } from "next/font/google";
import "../fonts.css";
import { sendOtp, verifyOtp, checkClient } from "@/services/booking.service";
import toast from "react-hot-toast";
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';

// Custom styles for PhoneInput
const phoneInputStyles = `
  .PhoneInput {
    display: flex;
    align-items: center;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    background-color: white;
  }

  .PhoneInputInput {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    padding-left: 0.5rem;
  }

  .PhoneInputCountry {
    margin-right: 0.5rem;
  }

  .PhoneInput--disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
  }

  .PhoneInputInput:disabled {
    background-color: transparent;
    cursor: not-allowed;
  }
`;

const roboto = Roboto({
  weight: ["400", "500", "700"],
  subsets: ["latin"],
  display: "swap",
});
interface BookingData {
  therapistId: string;
  therapistIdentifier: string;
  therapistName: string;
  therapistFullName: string;
  therapistPronouns: string;
  therapistProfileImage: string;
  therapistExperience: string;
  therapistDesignation: string;
  sessionMode: string;
  sessionDuration: string;
  slotType?: string;
  date: string;
  formattedDate: string;
  startTime: string;
  endTime: string;
  duration: number;
  sessionFee: number;
  minFee?: number;
  maxFee?: number;
  formattedStartTime: string;
  bookingMessage?: {
    message?: string;
    paymentInfo?: string;
    paymentMethod?: string;
  } | string;
}
interface TherapistProfileData {
  _id: string;
  identifier: string;
  name: string;
  fullName: string;
  pronouns: string;
  profileImage: string;
  experience: string;
  designation: string;
  minFee: number;
  maxFee?: number;
  bookingMessage?: {
    message?: string;
    paymentInfo?: string;
    paymentMethod?: string;
  } | string;
}

export default function BookingConfirmationPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { id } = params;
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [nameError, setNameError] = useState("");
  const [gender, setGender] = useState("");
  const [age, setAge] = useState("");
  const [ageError, setAgeError] = useState("");
  const [phoneNumber, setPhoneNumber] = useState<string | undefined>();

  // OTP verification state
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [otp, setOtp] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [isSendingOtp, setIsSendingOtp] = useState(false);

  // Client existence state
  const [isExistingClient, setIsExistingClient] = useState<boolean | null>(null);

  // Form disabled state - form is disabled until email is verified
  const [isFormDisabled, setIsFormDisabled] = useState(true);

  // Booking data from localStorage
  const [bookingData, setBookingData] = useState<BookingData | null>(null);
  const [therapistProfileData, setTherapistProfileData] = useState<TherapistProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Get booking data from localStorage
    const storedData = localStorage.getItem("bookingData");
    const hasBookingCompleted = localStorage.getItem("bookingCompleted");

    // Only set booking success if we have both the completed flag AND booking data
    // This prevents showing success when starting a new booking flow
    if (hasBookingCompleted === "true" && storedData) {
      setBookingSuccess(true);
    }

    if (storedData) {
      try {
        const parsedData = JSON.parse(storedData);
        setBookingData(parsedData);

        // Create therapist profile data for TherapistProfileBlock component
        setTherapistProfileData({
          _id: parsedData.therapistId,
          identifier: parsedData.therapistIdentifier,
          name: parsedData.therapistName,
          fullName: parsedData.therapistFullName,
          pronouns: parsedData.therapistPronouns,
          profileImage: parsedData.therapistProfileImage,
          experience: parsedData.therapistExperience,
          designation: parsedData.therapistDesignation,
          minFee: parsedData.minFee || parsedData.sessionFee,
          maxFee: parsedData.maxFee || parsedData.sessionFee,
          bookingMessage: parsedData.bookingMessage || ""
        });

        // If we have a booking message in the parsed data, update the booking message state
        if (parsedData.bookingMessage) {
          let updatedMessage;
          if (typeof parsedData.bookingMessage === 'string') {
            updatedMessage = {
              message: parsedData.bookingMessage,
              paymentInfo: "",
              paymentMethod: ""
            };
          } else {
            updatedMessage = {
              message: parsedData.bookingMessage.message || "",
              paymentInfo: parsedData.bookingMessage.paymentInfo || "",
              paymentMethod: parsedData.bookingMessage.paymentMethod || ""
            };
          }

          setBookingMessage(updatedMessage);
          localStorage.setItem("bookingMessage", JSON.stringify(updatedMessage));
        }
      } catch (error) {
        console.error("Error parsing booking data:", error);
        router.push(`/clients/${id}/slot-selection`);
      }
    }  else {
        router.push(`/clients/${id}/slot-selection`);
    }
    setIsLoading(false);
  }, [id, router]);

  // Handle email verification
  const handleSendOtp = async () => {
    if (!email) {
      toast.error("Please enter your email address");
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    setIsSendingOtp(true);
    try {
      await sendOtp(email);
      setIsVerifying(true);
      toast.success("Verification code sent! Please check your email.");
    } catch (error) {
      console.error("Error sending OTP:", error);
    } finally {
      setIsSendingOtp(false);
    }
  };

  // Handle OTP verification
  const handleVerifyOtp = async () => {
    if (!otp) {
      toast.error("Please enter the verification code");
      return;
    }

    try {
      await verifyOtp({ email, otp });
      setIsEmailVerified(true);
      setIsVerifying(false);
      setIsFormDisabled(false); // Enable the form after email verification
      toast.success("Email verified successfully!");

      // Check if client exists after successful email verification
      if (bookingData?.therapistId) {
        try {
          const clientCheckResponse = await checkClient({
            clientEmail: email,
            therapistId: bookingData.therapistId
          });

          let clientExists = false;
          if (clientCheckResponse.data?.clientExists !== undefined) {
            clientExists = clientCheckResponse.data.clientExists;
          }
          else if ('clientExists' in clientCheckResponse && typeof (clientCheckResponse as Record<string, unknown>).clientExists === 'boolean') {
            clientExists = (clientCheckResponse as Record<string, unknown>).clientExists as boolean;
          }
          else if (typeof clientCheckResponse === 'boolean') {
            clientExists = clientCheckResponse;
          }
          setIsExistingClient(clientExists);
        } catch (error) {
          console.error("Error checking client existence:", error);
          setIsExistingClient(false);
        }
      }
    } catch (error) {
      console.error("Error verifying OTP:", error);
      toast.error("Invalid verification code. Please try again.");
    }
  };

  // State for booking success
  const [bookingSuccess, setBookingSuccess] = useState(false);
  const [bookingMessage, setBookingMessage] = useState({
    message: "",
    paymentInfo: "",
    paymentMethod: ""
  });

  // Try to load saved booking message from localStorage if it exists
  useEffect(() => {
    const savedBookingMessage = localStorage.getItem("bookingMessage");
    if (savedBookingMessage) {
      try {
        const parsedMessage = JSON.parse(savedBookingMessage);
        setBookingMessage(parsedMessage);
      } catch (error) {
        console.error("Error parsing saved booking message:", error);
      }
    }
  }, []);

  // Check if page is being refreshed after successful booking
  useEffect(() => {
    // Get booking data from localStorage first
    const storedData = localStorage.getItem("bookingData");

    // Check if this is a page refresh after booking completion
    const hasBookingCompleted = localStorage.getItem("bookingCompleted");

    // Redirect to therapist profile in two cases:
    // 1. If it's a page refresh after booking completion (no data but has completed flag)
    // 2. If there's no booking data at all (user navigated directly to this page)
    if (!storedData) {
      if (hasBookingCompleted === "true") {
        // Case 1: Completed booking but refreshed page
        router.push(`/clients/${id}`);
      } else {
        // Case 2: No booking data and not completed - redirect to slot selection
        router.push(`/clients/${id}/slot-selection`);
      }
    }

    return () => {};
  }, [id, router]);

  // Helper function to convert date and time to ISO format
  const convertToISODateTime = (dateStr: string, timeStr: string): string => {
    const [day, month, year] = dateStr.split('-').map(Number);
    const [hours, minutes] = timeStr.split(':').map(Number);

    const date = new Date(year, month - 1, day, hours, minutes, 0);
    return date.toISOString();
  };

  // Helper function to format session mode display
  const formatSessionMode = (sessionMode: string, bookingData?: BookingData | null): string => {
    if (!sessionMode) return sessionMode;

    // Get the actual duration from booking data (prioritize duration from time slot)
    const duration = bookingData?.duration || bookingData?.sessionDuration;
    const durationText = duration ? `${duration} minutes` : "time selected by therapist in their calendar setting";

    // Handle introductory sessions
    if (sessionMode.toLowerCase().includes("introductory")) {
      return `Introductory (${durationText})`;
    }

    // Handle consultation sessions
    if (sessionMode.toLowerCase().includes("consultation")) {
      return `Consultation (${durationText})`;
    }

    // For any other session types, return as is with the duration
    return `${sessionMode} (${durationText})`;
  };

  // Validate name (only letters, spaces, and some basic punctuation)
  const validateName = (name: string): boolean => {
    const nameRegex = /^[A-Za-z\s.',-]+$/;
    return nameRegex.test(name);
  };

  // Handle name change with validation
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setName(newName);

    if (newName && !validateName(newName)) {
      setNameError("Name should only contain letters, spaces, and basic punctuation");
    } else {
      setNameError("");
    }
  };

  // Validate age (must be a number between 1 and 120)
  const validateAge = (age: string): boolean => {
    const ageNum = parseInt(age, 10);
    return !isNaN(ageNum) && ageNum >= 1 && ageNum <= 120;
  };

  // Handle age change with validation
  const handleAgeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newAge = e.target.value;
    setAge(newAge);

    if (newAge && !validateAge(newAge)) {
      setAgeError("Age must be between 1 and 120");
    } else {
      setAgeError("");
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form fields
    if (!name) {
      toast.error("Please enter your name");
      return;
    }

    // Validate name format
    if (!validateName(name)) {
      toast.error("Name should only contain letters, spaces, and basic punctuation");
      return;
    }

    if (!email) {
      toast.error("Please enter your email address");
      return;
    }

    if (!isEmailVerified) {
      toast.error("Please verify your email address");
      return;
    }

    if (!gender) {
      toast.error("Please select your gender");
      return;
    }

    if (!age) {
      toast.error("Please enter your age");
      return;
    }

    // Validate age range
    if (!validateAge(age)) {
      toast.error("Age must be between 1 and 120");
      return;
    }

    if (!phoneNumber) {
      toast.error("Please enter your mobile number");
      return;
    }

    if (!bookingData) {
      toast.error("Booking details not found");
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert date and time to ISO format
      const fromDateISO = convertToISODateTime(bookingData.date, bookingData.startTime);
      const toDateISO = convertToISODateTime(bookingData.date, bookingData.endTime);

      // Prepare the booking payload for the new API
      const payload = {
        therapistId: bookingData.therapistId,
        email: email,
        sessionType: bookingData.sessionMode?.toLowerCase().includes("introductory") ? "introductory" : "consultancy",
        fromDate: fromDateISO,
        toDate: toDateISO,
        clientCountry: "India",
        name: name,
        phone: phoneNumber,
        gender: gender,
        age: age,
        amount: bookingData.sessionFee,
        description: "New Session",
        location: "online",
        isBefore: false,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || ""
      };

      // Log API URL and payload for debugging
      const apiUrl = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:5959/api/v1"}/therapist/session-booking`;

      // Make the API call
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();

      if (!response.ok) {
        // Extract the backend error message and show it in toast
        const errorMessage = data.errors?.[0]?.message || data.message || "Failed to confirm booking";
        toast.error(errorMessage);
        return;
      }

      // Set success state and message
      setBookingSuccess(true);

      // Get booking message from the therapist profile data
      if (data.data?.bookingMessage) {
        let updatedMessage;

        // Handle case where bookingMessage might be a string or an object
        if (typeof data.data.bookingMessage === 'string') {
          updatedMessage = {
            message: data.data.bookingMessage,
            paymentInfo: "",
            paymentMethod: ""
          };
        } else {
          // Handle as object
          updatedMessage = {
            message: data.data.bookingMessage.message || "",
            paymentInfo: data.data.bookingMessage.paymentInfo || "",
            paymentMethod: data.data.bookingMessage.paymentMethod || ""
          };
        }

        // Update state and save to localStorage
        setBookingMessage(updatedMessage);
        localStorage.setItem("bookingMessage", JSON.stringify(updatedMessage));
      } else {
        if (therapistProfileData && therapistProfileData.bookingMessage) {
          const profileBookingMessage = therapistProfileData.bookingMessage;

          let updatedMessage;
          if (typeof profileBookingMessage === 'string') {
            updatedMessage = {
              message: profileBookingMessage,
              paymentInfo: "",
              paymentMethod: ""
            };
          } else {
            updatedMessage = {
              message: profileBookingMessage.message || "",
              paymentInfo: profileBookingMessage.paymentInfo || "",
              paymentMethod: profileBookingMessage.paymentMethod || ""
            };
          }

          setBookingMessage(updatedMessage);
          localStorage.setItem("bookingMessage", JSON.stringify(updatedMessage));
        }
      }

      // Set booking completed flag in localStorage
      localStorage.setItem("bookingCompleted", "true");
      localStorage.removeItem("bookingData");

      toast.success("Your booking has been confirmed!");
    } catch (error) {
      console.error("Booking error:", error);
      // Show the backend error message in toast
      const errorMessage = error instanceof Error ? error.message : "Failed to confirm booking";
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div
        className={`bg-[#2A1B6D] min-h-screen text-white flex flex-col items-center justify-center p-4 md:p-6 ${roboto.className} relative`}
        style={{
          backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundBlendMode: "overlay",
          backgroundAttachment: "fixed",
        }}
      >
        <style jsx global>{phoneInputStyles}</style>
        <div className="flex justify-center mb-6">
          <Image
            src="/assets/images/newHome/therapist-profile-logo.png"
            alt="Thought Pudding Logo"
            width={150}
            height={40}
            className="h-auto"
          />
        </div>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white mb-4"></div>
          <p className="text-xl font-medium text-white">
            Loading booking details...
          </p>
        </div>
      </div>
    );
  }

  // Check if this is a page refresh after booking completion
  const hasBookingCompleted = localStorage.getItem("bookingCompleted");

  if (!bookingData && hasBookingCompleted === "true") {
    return (
      <div
        className={`bg-[#2A1B6D] min-h-screen text-white flex flex-col items-center justify-center p-4 md:p-6 ${roboto.className} relative`}
        style={{
          backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundBlendMode: "overlay",
          backgroundAttachment: "fixed",
        }}
      >
        <style jsx global>{phoneInputStyles}</style>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white mb-4"></div>
          <p className="text-xl font-medium text-white">
            Redirecting to therapist profile...
          </p>
        </div>
      </div>
    );
  }

  // Regular case - no booking data and not a completed booking
  if (!bookingData) {
    return (
      <div
        className={`bg-[#2A1B6D] min-h-screen text-white flex flex-col items-center justify-center p-4 md:p-6 ${roboto.className} relative`}
        style={{
          backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundBlendMode: "overlay",
          backgroundAttachment: "fixed",
        }}
      >
        <style jsx global>{phoneInputStyles}</style>
        <div className="text-black p-8 rounded-xl shadow-lg max-w-md w-full bg-white">
          <div className="flex justify-center mb-6">
            <Image
              src="/assets/images/newHome/therapist-profile-logo.png"
              alt="Thought Pudding Logo"
              width={150}
              height={40}
              className="h-auto"
            />
          </div>
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600 mb-4">
              Booking Details Not Found
            </h2>
            <p className="text-black mb-6">
              We could not find your booking details. Please go back and select a slot again.
            </p>
            <button
              onClick={() => router.push(`/clients/${id}/slot-selection`)}
              className="bg-[#2C58BB] text-white px-4 py-2 rounded-md hover:bg-[#1E3A7B] transition-colors"
            >
              Go Back to Slot Selection
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-[#2A1B6D] min-h-screen text-white p-3 md:p-6 ${roboto.className} relative`}
      style={{
        backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundBlendMode: "overlay",
        backgroundAttachment: "fixed",
      }}
    >
      <style jsx global>{phoneInputStyles}</style>
      {/* Main Container */}
      <div className="w-full mx-auto max-w-[1200px] mb-8 md:mt-8 md:mx-auto">
        {/* Mobile Logo - Only visible on mobile at top left */}
        <div className="flex justify-start mt-0 mb-2 items-center sm:hidden">
          <p className="text-sm text-white mr-2">Powered by</p>
          <Image
            src="/assets/images/newHome/therapist-profile-logo.png"
            alt="Thought Pudding"
            width={100}
            height={25}
            className="h-auto"
          />
        </div>

        {/* Content Container */}
        <div
          className="w-full rounded-xl border border-gray-200 overflow-hidden p-3 md:p-6 bg-white"
          style={{
            backgroundImage: "url('/assets/images/newHome/bg-home.png')",
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "repeat",
            backgroundBlendMode: "multiply",
            opacity: 1,
          }}
        >
          {/* Two Column Layout - Stack on mobile (profile first), side by side on desktop */}
          <div className="flex flex-col md:flex-row md:p-6 md:gap-8">

          {/* Mobile Profile Section - Only visible on mobile at top */}
          <div className="md:hidden w-[94%] bg-white p-4 text-black flex items-center justify-center max-h-[180px] mx-auto my-3 rounded-lg shadow-md border border-gray-200">
            {therapistProfileData && (
              <div className="flex flex-col items-center justify-center w-full py-2">
                <div className="w-14 h-14 rounded-full overflow-hidden mb-1">
                  <Image
                    src={therapistProfileData.profileImage || "/assets/images/default-profile.png"}
                    alt={therapistProfileData.fullName}
                    width={56}
                    height={56}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex items-center justify-center gap-1 mb-0.5">
                  <h3 className="font-bold text-sm text-center">
                    {therapistProfileData.fullName}
                  </h3>
                  <p className="text-xs text-gray-600 text-center">
                    ({therapistProfileData.pronouns})
                  </p>
                </div>
                <p className="text-xs text-gray-600 mb-0.5 text-center">
                  {therapistProfileData.designation}
                </p>
                <p className="text-xs text-[#27AE60] font-medium mb-2 text-center">
                  {therapistProfileData.experience}+ year of experience
                </p>
                <button
                  onClick={() => router.push(`/clients/${id}`)}
                  className="bg-[#2C58BB] text-white rounded-full px-3 py-1 text-xs font-medium hover:bg-[#718FFF] transition"
                >
                  View Profile
                </button>
              </div>
            )}
          </div>

          {/* Left Section - Booking Details and Form */}
          <div className="w-[94%] md:w-2/3 bg-white p-4 md:p-8 text-black mx-auto md:mx-0 my-3 md:my-0 rounded-lg shadow-md border border-gray-200">
            {!bookingSuccess ? (
              <>
                <h1 className="text-xl font-bold mb-6 text-[#251D5C]">
                  Confirm Your Booking
                </h1>

                {/* Booking Summary */}
                <div className="border border-gray-200 rounded-md p-3 mb-6">
                  <div className="flex flex-col space-y-3 md:w-full">
                    <div className="grid grid-cols-[100px_1fr] md:grid-cols-[120px_1fr]">
                      <span className="font-medium">Session Mode</span>
                      <span className="text-gray-700">: {formatSessionMode(bookingData.sessionMode, bookingData)}</span>
                    </div>
                    <div className="grid grid-cols-[100px_1fr] md:grid-cols-[120px_1fr]">
                      <span className="font-medium">Date</span>
                      <span className="text-gray-700">: {bookingData.formattedDate}</span>
                    </div>
                    <div className="grid grid-cols-[100px_1fr] md:grid-cols-[120px_1fr]">
                      <span className="font-medium">Time</span>
                      <span className="text-gray-700">: {bookingData.formattedStartTime}</span>
                    </div>
                    <div className="grid grid-cols-[100px_1fr] md:grid-cols-[120px_1fr]">
                      <span className="font-medium">Session Fee</span>
                      <span className="text-gray-700">: <span className="font-bold">
                        {bookingData.sessionFee === 0 ? "Free" : `₹ ${bookingData.minFee || bookingData.sessionFee}${bookingData.maxFee && bookingData.maxFee !== bookingData.minFee ? ` - ₹ ${bookingData.maxFee}` : ""}`}
                      </span></span>
                    </div>
                  </div>
                </div>

                {/* Note */}
                <div className="mb-6 text-sm bg-[#F0F5FF] p-3 rounded-md border border-[#E0E8FF]">
                  <p className="font-medium mb-1 text-[#2C58BB]">Note :</p>
                  <p className="text-gray-700">
                    Thought Pudding (for therapists) helps therapists manage their private practice. Feel free to reach out to your therapist directly in case of any concerns.
                  </p>
                </div>

                {/* Form */}
                <div className="mt-6">
                  <h2 className="text-lg font-semibold mb-4">Fill Up this form</h2>

                  {/* Email Address */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Email Address</label>
                    {!isVerifying && !isEmailVerified && (
                      <div className="relative">
                        <input
                          type="email"
                          placeholder="Enter your email address"
                          className="w-full border border-gray-300 rounded-md px-3 py-2 pr-[80px] focus:outline-none focus:ring-1 focus:ring-blue-500"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          <button
                            onClick={handleSendOtp}
                            disabled={isSendingOtp}
                            className="text-[#2C58BB] text-sm font-medium underline"
                          >
                            {isSendingOtp ? "Sending..." : "Verify"}
                          </button>
                        </div>
                      </div>
                    )}

                    {/* OTP Verification */}
                    {isVerifying && !isEmailVerified && (
                      <div className="flex flex-col">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-700">Enter OTP</span>
                          <button
                            onClick={() => setIsVerifying(false)}
                            className="text-[#2C58BB] text-sm font-medium underline"
                          >
                            Edit Email
                          </button>
                        </div>
                        <div className="relative">
                          <input
                            type="text"
                            placeholder="Enter OTP"
                            className="w-full border border-gray-300 rounded-md px-3 py-2 pr-[140px] sm:pr-[160px] focus:outline-none focus:ring-1 focus:ring-blue-500"
                            value={otp}
                            onChange={(e) => setOtp(e.target.value)}
                          />
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2 sm:space-x-4">
                            <button
                              onClick={handleSendOtp}
                              disabled={isSendingOtp}
                              className="text-[#2C58BB] text-xs sm:text-sm font-medium underline whitespace-nowrap"
                            >
                              Resend
                            </button>
                            <button
                              onClick={handleVerifyOtp}
                              className="text-[#2C58BB] text-xs sm:text-sm font-medium underline whitespace-nowrap"
                            >
                              Submit
                            </button>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Verified Email Display */}
                    {isEmailVerified && (
                      <div className="relative">
                        <input
                          type="text"
                          value={email}
                          disabled
                          className="w-full border border-gray-300 rounded-md px-3 py-2 pr-[80px] bg-gray-50 text-gray-700"
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center">
                          <span className="text-green-600 text-xs sm:text-sm font-medium">Verified</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Your Name */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Your Name</label>
                    <input
                      type="text"
                      placeholder="Enter your name"
                      className={`w-full border ${nameError ? 'border-red-500' : 'border-gray-300'} rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 ${isFormDisabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                      value={name}
                      onChange={handleNameChange}
                      disabled={isFormDisabled}
                    />
                    {nameError && <p className="text-red-500 text-xs mt-1">{nameError}</p>}
                  </div>

                  {/* Gender and Age */}
                  <div className="flex flex-col sm:flex-row gap-4 mb-4">
                    <div className="flex-1">
                      <label className="block text-sm font-medium mb-1">Gender</label>
                      <select
                        className={`w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none ${isFormDisabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`}
                        value={gender}
                        onChange={(e) => setGender(e.target.value)}
                        disabled={isFormDisabled}
                      >
                        <option value="">Select Gender</option>
                        <option value="Cisgender Male">Cisgender Male</option>
                        <option value="Cisgender Female">Cisgender Female</option>
                        <option value="Transgender">Transgender</option>
                        <option value="Non-Binary">Non-Binary</option>
                      </select>
                    </div>
                    <div className="flex-1">
                      <label className="block text-sm font-medium mb-1">Age</label>
                      <input
                        type="number"
                        placeholder="Enter your age"
                        className={`w-full border ${ageError ? 'border-red-500' : 'border-gray-300'} rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500 ${isFormDisabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                        value={age}
                        onChange={handleAgeChange}
                        min="1"
                        max="120"
                        disabled={isFormDisabled}
                      />
                      {ageError && <p className="text-red-500 text-xs mt-1">{ageError}</p>}
                    </div>
                  </div>

                  {/* Mobile Number */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium mb-1">Mobile Number</label>
                    <div className={`${isFormDisabled ? 'opacity-70' : ''}`}>
                      <div className={`border border-gray-300 rounded-md ${isFormDisabled ? 'bg-gray-100' : ''}`}>
                        <PhoneInput
                          international
                          defaultCountry="IN"
                          placeholder="Enter phone number"
                          value={phoneNumber}
                          onChange={setPhoneNumber}
                          disabled={isFormDisabled}
                          className={isFormDisabled ? 'PhoneInput--disabled' : ''}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Proceed Button */}
                  <div className="flex justify-end">
                    <button
                      onClick={handleSubmit}
                      disabled={isSubmitting || !isEmailVerified || !!nameError || !!ageError}
                      className={`w-auto px-8 py-2.5 rounded-md ${
                        isEmailVerified && !nameError && !ageError
                          ? "bg-[#2C58BB] text-white hover:bg-[#1E3A7B]"
                          : "bg-gray-300 text-gray-500 cursor-not-allowed"
                      } transition-colors`}
                    >
                      {isSubmitting ? "Processing..." : "Continue"}
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Success Message */}
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-bold text-gray-800">
                    {isExistingClient === true
                      ? "Your Session is Reserved"
                      : isExistingClient === false
                        ? "Your Session Request is Sent"
                        : "Your Session Is Confirmed!"
                    }
                  </h2>
                </div>

                <div className="mb-6">
                  <h3 className="font-semibold text-gray-800 mb-2">Message From Your Therapist</h3>
                  {bookingMessage.message ? (
                    <>
                      <p className="text-gray-700 mb-2">{bookingMessage.message}</p>
                      {bookingMessage.paymentInfo && (
                        <p className="text-gray-700">{bookingMessage.paymentInfo}</p>
                      )}
                      {bookingMessage.paymentMethod && (
                        <p className="text-gray-700 mt-2">Payment Method: <span className="font-semibold">{bookingMessage.paymentMethod}</span></p>
                      )}
                    </>
                  ) : (
                    <p className="text-gray-700 mb-2">Your session has been confirmed. The therapist will contact you with further details.</p>
                  )}
                </div>

                {/* Booking Summary in Success View */}
                <div className="bg-[#FFF9F0] rounded-md p-4 mb-6">
                  <div className="grid grid-cols-[120px_1fr] gap-y-2">
                    <span className="font-medium">Session Mode</span>
                    <span>: {formatSessionMode(bookingData.sessionMode, bookingData)}</span>

                    <span className="font-medium">Date</span>
                    <span>: {bookingData.formattedDate}</span>

                    <span className="font-medium">Time</span>
                    <span>: {bookingData.formattedStartTime}</span>

                    <span className="font-medium">Session Fee</span>
                    <span>: <span className="font-bold">
                      {bookingData.sessionFee === 0 ? "Free" : `₹ ${bookingData.minFee}${bookingData.maxFee && bookingData.maxFee !== bookingData.minFee ? ` - ₹ ${bookingData.maxFee}` : ""}`}
                    </span></span>
                  </div>
                </div>
              </>
            )}
          </div>

          {/* Right Section - Therapist Profile (Desktop Only) */}
          <div className="hidden md:flex w-1/3 bg-white p-6 text-black items-center justify-center max-h-[250px] self-start rounded-lg shadow-md border border-gray-200 my-3">
            {therapistProfileData && (
              <div className="flex flex-col items-center justify-center w-full">
                <div className="w-16 h-16 rounded-full overflow-hidden mb-2">
                  <Image
                    src={therapistProfileData.profileImage || "/assets/images/default-profile.png"}
                    alt={therapistProfileData.fullName}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex items-center justify-center gap-1 mb-1">
                  <h3 className="font-bold text-base text-center">
                    {therapistProfileData.fullName}
                  </h3>
                  <p className="text-sm text-gray-600 text-center">
                    ({therapistProfileData.pronouns})
                  </p>
                </div>
                <p className="text-sm text-gray-600 mb-1 text-center">
                  {therapistProfileData.designation}
                </p>
                <p className="text-sm text-[#27AE60] font-medium mb-3 text-center">
                  {therapistProfileData.experience}+ year of experience
                </p>
                <button
                  onClick={() => router.push(`/clients/${id}`)}
                  className="bg-[#2C58BB] text-white rounded-full px-4 py-1.5 text-sm font-medium hover:bg-[#718FFF] transition"
                >
                  View Profile
                </button>
              </div>
            )}
          </div>
          </div>
        </div>

        {/* Footer Logo - Only visible on desktop */}
        <div className="hidden md:flex justify-end mt-4 mb-2 items-center">
          <p className="text-sm text-white mr-2">Powered by</p>
          <Image
            src="/assets/images/newHome/therapist-profile-logo.png"
            alt="Thought Pudding"
            width={120}
            height={30}
            className="h-auto"
          />
        </div>
      </div>
    </div>
  );
}
