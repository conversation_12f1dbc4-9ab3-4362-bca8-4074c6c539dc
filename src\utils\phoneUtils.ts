/**
 * Utility functions for phone number processing
 */

/**
 * Extracts 10-digit mobile number from various phone number formats
 * Removes country codes and returns only the 10-digit number
 * @param phoneNumber - Phone number in various formats (with/without country code)
 * @returns 10-digit mobile number or null if invalid
 */
export const extractTenDigitMobile = (phoneNumber: string | undefined): string | null => {
  if (!phoneNumber) return null;
  
  // Remove all non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  // If it's exactly 10 digits, return as is
  if (digitsOnly.length === 10) {
    return digitsOnly;
  }
  
  // If it's 11 digits and starts with 91 (India country code), extract last 10
  if (digitsOnly.length === 11 && digitsOnly.startsWith('91')) {
    return digitsOnly.substring(1);
  }
  
  // If it's 12 digits and starts with 091, extract last 10
  if (digitsOnly.length === 12 && digitsOnly.startsWith('091')) {
    return digitsOnly.substring(2);
  }
  
  // If it's 13 digits and starts with +91, extract last 10
  if (digitsOnly.length === 13 && phoneNumber.startsWith('+91')) {
    return digitsOnly.substring(3);
  }
  
  // If it's more than 10 digits, try to extract the last 10
  if (digitsOnly.length > 10) {
    const lastTen = digitsOnly.substring(digitsOnly.length - 10);
    // Validate that it's a valid Indian mobile number (starts with 6, 7, 8, or 9)
    if (/^[6-9]\d{9}$/.test(lastTen)) {
      return lastTen;
    }
  }
  
  return null;
};

/**
 * Validates if a phone number is a valid 10-digit Indian mobile number
 * @param phoneNumber - Phone number to validate
 * @returns true if valid, false otherwise
 */
export const validateIndianMobile = (phoneNumber: string | undefined): boolean => {
  if (!phoneNumber) return false;
  
  const tenDigitNumber = extractTenDigitMobile(phoneNumber);
  if (!tenDigitNumber) return false;
  
  // Indian mobile numbers start with 6, 7, 8, or 9 and are exactly 10 digits
  return /^[6-9]\d{9}$/.test(tenDigitNumber);
};

/**
 * Formats a phone number for display (adds +91 prefix)
 * @param phoneNumber - 10-digit mobile number
 * @returns formatted phone number with +91 prefix
 */
export const formatPhoneForDisplay = (phoneNumber: string): string => {
  const tenDigitNumber = extractTenDigitMobile(phoneNumber);
  if (!tenDigitNumber) return phoneNumber;
  
  return `+91${tenDigitNumber}`;
};

/**
 * Gets error message for invalid phone numbers
 * @param phoneNumber - Phone number to validate
 * @returns error message or null if valid
 */
export const getPhoneValidationError = (phoneNumber: string | undefined): string | null => {
  if (!phoneNumber) {
    return "Mobile number is required";
  }
  
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  if (digitsOnly.length === 0) {
    return "Mobile number is required";
  }
  
  if (digitsOnly.length < 10) {
    return "Mobile number must be at least 10 digits";
  }
  
  const tenDigitNumber = extractTenDigitMobile(phoneNumber);
  if (!tenDigitNumber) {
    return "Invalid mobile number format";
  }
  
  if (!/^[6-9]\d{9}$/.test(tenDigitNumber)) {
    return "Mobile number must start with 6, 7, 8, or 9";
  }
  
  return null;
};
