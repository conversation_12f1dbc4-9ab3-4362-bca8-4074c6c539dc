import { X } from "@phosphor-icons/react";
import React, { useEffect, useState } from "react";
import Button from "../common/Button";
import Input from "../common/Input";
import SelectDropdown from "../common/SelectDropdown";
import DatePicker from "../common/DatePicker";
import TimePicker from "../common/TimePicker";
import { useFormik } from "formik";
import * as Yup from "yup";
import { createSchedule, getClientDetails } from "@/services/dashboard.service";
import moment from "moment";
// import toast from "react-hot-toast";
import { addMonths } from "@/helper/MomentHelper";

const genderOption = [
  "Cisgender Male",
  "Cisgender Female",
  "Transgender",
  "Non-Binary",
];

interface ClientDetails {
  email: string;
  phone?: string;
  age?: number;
  gender?: string;
  name?: string;
  defaultSessionAmount?: number;
}
const reminderOption = ["10-Minutes", "20-Minutes", "30-Minutes"];
// Validation Schema using Yup

const validationSchema = Yup.object({
  fromDate: Yup.date().required("Appointment date is required"),
  startTime: Yup.string()
    .required("Start time is required")
    .matches(
      /^([01]\d|2[0-3]):([0-5]\d)$/,
      "Start time must be in HH:mm format"
    )
    .test("is-valid", "Start time must be before end time.", function (value) {
      const { endTime } = this.parent; // Access sibling field value
      if (!value || !endTime) return true; // Skip if either field is empty
      return value < endTime; // Ensure startTime is earlier than endTime
    }),
  endTime: Yup.string()
    .required("End time is required")
    .matches(/^([01]\d|2[0-3]):([0-5]\d)$/, "End time must be in HH:mm format")
    .test("is-greater", "End time must be after start time.", function (value) {
      const { startTime } = this.parent; // Access sibling field value
      if (!startTime || !value) return true; // Skip if either field is empty
      return startTime < value; // Ensure endTime is later than startTime
    }),
  recurrence: Yup.string().required("Frequency is required"),
  name: Yup.string().required("Full name is required"),
  phone: Yup.string()
    .matches(/^[0-9]+$/, "Only digits are allowed")
    .min(10, "Mobile number must be at least 10 digits")
    .max(10, "Mobile number maximum 10 digits"),
  emails: Yup.array()
    .of(Yup.string().email("Invalid email address"))
    .min(1, "Email is required"),
  age: Yup.number().positive("Age must be a positive number"),
  amount: Yup.number()
    .positive("Payment must be a positive number")
    .required("Payment is required"),
  reminder: Yup.string().required("Reminder is required"),
  summary: Yup.string().required("Description is required"),
});

const MomentHelper = {
  getDateIST(date: string) {
    return moment(date).format("YYYY-MM-DD");
  },

  getTimeIST(time: string) {
    return moment(time).format("HH:mm");
  },

  convertToIST(date: string, time: string) {
    const [year, month, day] = date.split("-");
    const [hours, minutes] = time.split(":");
    const isoDate = new Date(
      Number(year),
      Number(month) - 1,
      Number(day),
      Number(hours),
      Number(minutes),
      0
    ).toISOString();
    return isoDate;
  },
};

const ScheduleSessionSidebar: React.FC<{
  isScheduleSessionModal: boolean;
  setIsScheduleSessionModal: (value: boolean) => void;
}> = ({ isScheduleSessionModal, setIsScheduleSessionModal }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [startDay, setStartDay] = useState<string>("Monday");

  const frequencyOption = [
    `Every Week On ${startDay}`,
    `Every Two Weeks ${startDay}`,
    "Does not repeat",
    // "Every Day",
  ];

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      fromDate: "",
      startTime: "",
      endTime: "",
      recurrence: "",
      toDate: "",
      name: "",
      phone: "",
      emails: [],
      age: "",
      gender: "",
      amount: "",
      reminder: "",
      summary: "",
    },
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      setIsSubmitting(true);

      // Destructure resetForm from Formik
      const { startTime, endTime, reminder, ...restValues } = values;
      console.log({ startTime, endTime, reminder });

      const startDate = MomentHelper.getDateIST(values?.fromDate);
      const fromDates = MomentHelper.convertToIST(startDate, values?.startTime);
      const endDate = addMonths(startDate, 3);

      const toDates = MomentHelper.convertToIST(endDate, values?.endTime);

      const formData = {
        ...restValues,
        clientCountry: "India",
        sessionDate: fromDates,
        description: "new session",
        location: "online",
        isBefore: false,
        fromDate: fromDates,
        toDate: toDates,
      };

      // Add your form submit logic here
      const data = await createSchedule(formData);
      setIsSubmitting(false);

      // Reset the form, including startTime and endTime
      if (data.success) {
        setIsScheduleSessionModal(false);
        resetForm();

        // Refresh the page
        window.location.reload();
      }
    },
  });

  const [emailSuggestions, setEmailSuggestions] = useState<ClientDetails[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isNewClient, setIsNewClient] = useState(false);

  const handleEmailInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    setSearchQuery(input);
    formik.setFieldValue("emails", [input]);

    if (input.length >= 1) {
      try {
        const clientDetails = await getClientDetails(input);

        if (clientDetails?.data?.clients?.length > 0) {
          setEmailSuggestions(clientDetails.data.clients);
          setShowSuggestions(true);
          setIsNewClient(false);
        } else {
          setEmailSuggestions([]);
          setShowSuggestions(false);
          setIsNewClient(true);
        }

      } catch (error) {
        console.error("Error fetching email suggestions:", error);
        setEmailSuggestions([]);
        setShowSuggestions(false);
        setIsNewClient(true);
      }
    } else {
      setEmailSuggestions([]);
      setShowSuggestions(false);
      setIsNewClient(false);
    }
  };


  const handleSuggestionClick = (email: string, details: ClientDetails) => {
    setSearchQuery(email);
    formik.setFieldValue("emails", [email]);
    setShowSuggestions(false);

    if (details.phone) formik.setFieldValue("phone", details.phone);
    if (details.age !== undefined) formik.setFieldValue("age", details.age);
    if (details.gender) formik.setFieldValue("gender", details.gender);
    if (details.defaultSessionAmount !== undefined) formik.setFieldValue("amount", details.defaultSessionAmount);
    if (details.name) formik.setFieldValue("name", details.name);
  };

  const handleEmailBlur = async (email: string) => {
    if (formik.errors.emails) {
      return;
    }
    try {
      const clientDetails = await getClientDetails(email);

      if (clientDetails) {
        if (clientDetails.phone) {
          formik.setFieldValue("phone", clientDetails.phone);
        }
        if (clientDetails.age) {
          formik.setFieldValue("age", clientDetails.age);
        }
        if (clientDetails.gender) {
          formik.setFieldValue("gender", clientDetails.gender);
        }
        if (clientDetails.defaultSessionAmount) {
          await formik.setFieldValue(
            "amount",
            Number(clientDetails.defaultSessionAmount)
          );
          setTimeout(async () => {
            await formik.validateField("amount");
          }, 0);
        }
        if (clientDetails.name) {
          await formik.setFieldValue("name", clientDetails.name);
          setTimeout(async () => {
            await formik.validateField("name");
          }, 0);
        }
      }
    } catch (error) {
      console.error("Error fetching client details:", error);
    }
  };

  useEffect(() => {
    const handleBodyScroll = (shouldLock: boolean) => {
      if (shouldLock) {
        document.body.style.overflow = "hidden";
      } else {
        document.body.style.overflow = "";
      }
    };

    handleBodyScroll(isScheduleSessionModal);

    return () => handleBodyScroll(false);
  }, [isScheduleSessionModal]);

  return (
    <div
      className={`fixed w-full h-full bg-black/20 top-0 left-0 z-[999] ${
        isScheduleSessionModal ? "visible" : "invisible"
      }`}
      onClick={() => {
        setIsScheduleSessionModal(false);
        formik.resetForm();
      }}
    >
      <div
        className={`max-w-[416px] w-full  bg-white absolute top-0 right-0 h-full transition-all duration-300 ${
          isScheduleSessionModal ? "translate-x-0" : "translate-x-full"
        }`}
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside the sidebar
      >
        <div className="relative flex flex-col h-[100svh] sm:h-screen overflow-y-auto">
          {/* side bar header */}
          <div className="px-5 py-3.5 shadow-[0px_4px_12px_0px_#0000000F] flex justify-between items-center sticky top-0">
            <h3 className="text-lg font-medium text-[#242424]">
              Schedule Session
            </h3>
            <button
              onClick={() => {
                setIsScheduleSessionModal(false);
                formik.resetForm();
              }}
            >
              <X size={20} />
            </button>
          </div>
          {/* Loader overlay */}
          {isSubmitting && (
            <div className="absolute inset-0 flex justify-center items-center bg-black/50 z-[1000]">
              <div className="spinner-border animate-spin h-12 w-12 border-4 border-t-transparent border-white rounded-full"></div>
            </div>
          )}

          {/* content */}
          <form
            onSubmit={formik.handleSubmit}
            className="p-5 flex-1 overflow-auto"
          >
            <div>
              <h4 className="text-sm/5 font-medium text-[#5E585A]">
                Clients details
              </h4>
              <div className="grid grid-cols-2 gap-5 mt-[15px]">
                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Full Name <span className="text-red-600">*</span>
                  </label>
                  <Input
                    name="name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter Full Name"
                  />
                  {formik.touched.name && formik.errors.name ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.name}
                    </div>
                  ) : null}
                </div>

                <div className="col-span-2 relative">
                  <label className="text-sm/5 text-primary font-medium">
                    Email <span className="text-red-600">*</span>
                  </label>
                  <Input
                    name="emails"
                    value={searchQuery}
                    onChange={handleEmailInputChange}
                    onBlur={(e) => handleEmailBlur(e.target.value)}
                    placeholder="Enter Email"
                    type="email"
                  />
                  {showSuggestions && emailSuggestions.length > 0 && (
                  <ul className="absolute bg-white border border-gray-300 w-full z-50 shadow-lg max-h-40 overflow-y-auto">
                    {emailSuggestions.map((suggestion, index) => {
                      return (
                        <li
                          key={index}
                          onMouseDown={(e) => e.preventDefault()} // Prevent dropdown from closing before selection
                          onClick={() => handleSuggestionClick(suggestion.email, suggestion)}
                          className="p-2 hover:bg-gray-100 cursor-pointer"
                        >
                          {suggestion.email}
                        </li>
                      );
                    })}
                  </ul>
                )}
                {isNewClient && (
                  <div className="mt-2 inline-block bg-[#C58843] text-white text-xs font-medium px-3 py-1 rounded-full shake-animation">
                    New Client Detected
                  </div>
                )}
                  {formik.touched.emails && formik.errors.emails ? (
                    <div className="text-red-600 text-sm">{formik.errors.emails}</div>
                  ) : null}
                </div>

                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Mobile Number
                  </label>
                  <Input
                    name="phone"
                    value={formik.values.phone}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter Number"
                    type="tel"
                  />
                  {formik.touched.phone && formik.errors.phone ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.phone}
                    </div>
                  ) : null}
                </div>

                <div>
                  <label className="text-sm/5 text-primary font-medium">
                    Age {/* <span className="text-red-600">*</span> */}
                  </label>
                  <Input
                    name="age"
                    value={formik.values.age}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter Age"
                    type="text"
                  />
                  {formik.touched.age && formik.errors.age ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.age}
                    </div>
                  ) : null}
                </div>
                <div>
                  <label className="text-sm/5 text-primary font-medium">
                    Gender {/* <span className="text-red-600">*</span> */}
                  </label>
                  <SelectDropdown
                    options={genderOption}
                    value={formik.values.gender}
                    onChange={(val) => formik.setFieldValue("gender", val)}
                    placeholder="Select ..."
                  />
                  {formik.touched.gender && formik.errors.gender ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.gender}
                    </div>
                  ) : null}
                </div>
                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Amount <span className="text-red-600">*</span>
                  </label>
                  <Input
                    name="amount"
                    value={formik.values.amount}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter amount"
                    type="number"
                  />
                  {formik.touched.amount && formik.errors.amount ? (
                    <div className="text-red-600 text-sm">
                      {formik.errors.amount}
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
            <hr className="border-divider my-5" />
            <div className="grid grid-cols-2 gap-5">
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Appointment Date <span className="text-red-600">*</span>
                  <DatePicker
                    placeholder="DD/MM/YYYY"
                    value={formik.values.fromDate}
                    minDateToday={true}
                    onChange={(val) => {
                      const dateValue = new Date(val); // Create a Date object from the string
                      // Get the day name (e.g., "Monday", "Tuesday")
                      const dayName = dateValue.toLocaleDateString("en-US", {
                        weekday: "long", // Full day name (Monday, Tuesday)
                      });
                      setStartDay(dayName);
                      if (formik.values.recurrence) {
                        formik.setFieldValue("recurrence", "");
                      }
                      formik.setFieldValue("fromDate", val);
                    }}
                  />
                </label>

                {formik.touched.fromDate && formik.errors.fromDate ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.fromDate}
                  </div>
                ) : null}
              </div>

              <div>
                <label className="text-sm/5 text-primary font-medium">
                  Start Time <span className="text-red-600">*</span>
                </label>
                <TimePicker
                  value={formik.values.startTime || ""}
                  fromDate={
                    formik.values.fromDate
                      ? new Date(formik.values.fromDate)
                      : null
                  } // Convert to Date if exists
                  onChange={(val) => {
                    formik.setFieldValue("startTime", val); // Set valid start time
                  }}
                />
                {formik.touched.startTime && formik.errors.startTime ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.startTime}
                  </div>
                ) : null}
              </div>

              <div>
                <label className="text-sm/5 text-primary font-medium">
                  End Time <span className="text-red-600">*</span>
                </label>
                <TimePicker
                  fromDate={
                    formik.values.fromDate
                      ? new Date(formik.values.fromDate)
                      : null
                  } // Convert to Date if exists
                  value={formik.values.endTime || ""}
                  onChange={(val) => {
                    formik.setFieldValue("endTime", val);
                  }}
                />
                {formik.touched.endTime && formik.errors.endTime ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.endTime}
                  </div>
                ) : null}
              </div>
              {/* <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  End Date
                </label>
                <DatePicker
                  minDateToday={true}
                  placeholder="DD/MM/YYYY"
                  value={formik.values.toDate}
                  minDate={formik.values.fromDate}
                  onChange={(val) => formik.setFieldValue("toDate", val)}
                />
                {formik.touched.toDate && formik.errors.toDate ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.toDate}
                  </div>
                ) : null}
              </div> */}
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Frequency <span className="text-red-600">*</span>
                </label>
                <SelectDropdown
                  options={frequencyOption}
                  value={formik.values.recurrence}
                  onChange={(val) => formik.setFieldValue("recurrence", val)}
                  placeholder="Select ..."
                />
                {formik.touched.recurrence && formik.errors.recurrence ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.recurrence}
                  </div>
                ) : null}
              </div>
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Description <span className="text-red-600">*</span>
                </label>
                <textarea
                  name="summary"
                  className="block w-full mt-2 p-3 border border-green-600/20 rounded-lg outline-none text-sm text-primary bg-transparent focus:border-green-600"
                  placeholder="Enter Description"
                  value={formik.values.summary}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                ></textarea>
                {formik.touched.summary && formik.errors.summary ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.summary}
                  </div>
                ) : null}
              </div>
              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Reminder <span className="text-red-600">*</span>
                </label>
                <SelectDropdown
                  options={reminderOption}
                  value={formik.values.reminder}
                  onChange={(val) => formik.setFieldValue("reminder", val)}
                  placeholder="Select ..."
                />
                {formik.touched.reminder && formik.errors.reminder ? (
                  <div className="text-red-600 text-sm">
                    {formik.errors.reminder}
                  </div>
                ) : null}
              </div>
            </div>
          </form>

          {/* side bar footer */}
          <div className="bg-white shadow-[0px_4px_43.4px_0px_#0000001A] px-5 py-2.5 grid grid-cols-2 gap-5 sticky bottom-0 z-10">
            <Button
              onClick={() => {
                formik.resetForm();
                setIsScheduleSessionModal(false);
              }}
              variant="outlinedGreen"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              onClick={formik.handleSubmit}
              variant="filledGreen"
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleSessionSidebar;
